<?php

test('management user can access management dashboard', function (): void {
    $estate = $this->createTestEstate();
    $user = $this->createManagerUser();

    $this->assignUserToEstate($user, $estate);
    $this->actingAs($user);

    $response = $this->get(route('management.dashboard'));

    $response->assertStatus(200);
});

test('reviewer user can access reviewer dashboard', function (): void {
    $estate = $this->createTestEstate();
    $user = $this->createReviewerUser();

    $this->assignUserToEstate($user, $estate);
    $this->actingAs($user);

    $response = $this->get(route('reviewer.dashboard'));

    $response->assertStatus(200);
});

test('caretaker user can access caretaker dashboard', function (): void {
    $estate = $this->createTestEstate();
    $user = $this->createCaretakerUser();

    $this->assignUserToEstate($user, $estate);
    $this->actingAs($user);

    $response = $this->get(route('caretaker.dashboard'));

    $response->assertStatus(200);
});

test('management user cannot access reviewer dashboard', function (): void {
    $user = $this->createManagerUser();
    $this->actingAs($user);

    $response = $this->get(route('reviewer.dashboard'));

    $response->assertStatus(403);
});

test('reviewer user cannot access management dashboard', function (): void {
    $user = $this->createReviewerUser();
    $this->actingAs($user);

    $response = $this->get(route('management.dashboard'));

    $response->assertStatus(403);
});
