<?php

use App\Models\User;
use App\Models\Estate;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('simple user creation works', function (): void {
    $user = User::factory()->create([
        'name' => 'Test User',
        'email' => '<EMAIL>',
    ]);

    expect($user->name)->toBe('Test User');
    expect($user->email)->toBe('<EMAIL>');
});

test('estate creation works', function (): void {
    $estate = Estate::factory()->create([
        'name' => 'Test Estate',
        'code' => 'TST-001',
    ]);

    expect($estate->name)->toBe('Test Estate');
    expect($estate->code)->toBe('TST-001');
});

test('user role assignment works', function (): void {
    // First create the role manually to avoid seeder
    \Spatie\Permission\Models\Role::create(['name' => 'manager', 'guard_name' => 'web']);

    $user = User::factory()->create();
    $user->assignRole('manager');

    expect($user->hasRole('manager'))->toBeTrue();
});

test('user estate assignment works', function (): void {
    $user = User::factory()->create();
    $estate = Estate::factory()->create();

    $user->assignedEstates()->attach($estate->id, [
        'assigned_from' => now()->toDateString(),
        'is_active' => true,
    ]);

    expect($user->assignedEstates)->toHaveCount(1);
    expect($user->assignedEstates->first()->id)->toBe($estate->id);
});

test('permission seeder causes memory issues', function (): void {
    // This test will help us identify if the seeder is the problem
    $this->seed(\Database\Seeders\PermissionSeeder::class);

    $user = User::factory()->create();
    $user->assignRole('admin');

    expect($user->hasRole('admin'))->toBeTrue();
})->skip('This test is expected to cause memory issues');
