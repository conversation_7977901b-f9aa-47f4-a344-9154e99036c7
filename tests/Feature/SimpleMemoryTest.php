<?php

use App\Models\User;
use App\Models\Estate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\Traits\CreatesTestUsers;

uses(RefreshDatabase::class);
uses(CreatesTestUsers::class);

test('simple user creation works', function (): void {
    $user = User::factory()->create([
        'name' => 'Test User',
        'email' => '<EMAIL>',
    ]);

    expect($user->name)->toBe('Test User');
    expect($user->email)->toBe('<EMAIL>');
});

test('simple permission check works', function (): void {
    $user = User::factory()->create();

    // This should not cause memory issues
    expect($user)->toBeInstanceOf(User::class);
});

test('estate creation works', function (): void {
    $estate = Estate::factory()->create([
        'name' => 'Test Estate',
        'code' => 'TST-001',
    ]);

    expect($estate->name)->toBe('Test Estate');
    expect($estate->code)->toBe('TST-001');
});

test('manager user creation works without memory issues', function (): void {
    $user = $this->createManagerUser();

    expect($user)->toBeInstanceOf(User::class);
    expect($user->hasRole('manager'))->toBeTrue();
});
