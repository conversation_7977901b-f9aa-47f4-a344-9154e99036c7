<?php

namespace Tests\Traits;

use App\Models\Estate;
use App\Models\User;
use Database\Seeders\TestPermissionSeeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

/**
 * Unified test setup trait that replaces multiple redundant traits
 * Optimized for memory efficiency and test isolation
 */
trait TestSetup
{
    /**
     * Static flag to track if permissions have been set up
     */
    private static bool $permissionsSetUp = false;

    /**
     * Set up permissions only once per test run to avoid memory issues
     */
    protected function setUpTestPermissions(): void
    {
        if (self::$permissionsSetUp) {
            return;
        }

        // Clear permission cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Run the minimal permission seeder once
        $this->seed(TestPermissionSeeder::class);

        self::$permissionsSetUp = true;
    }

    /**
     * Create admin user with minimal setup
     */
    protected function createAdminUser(array $attributes = []): User
    {
        $this->setUpTestPermissions();

        $user = User::factory()->create(array_merge([
            'email' => '<EMAIL>',
            'name' => 'Test Admin',
        ], $attributes));

        $user->assignRole('admin');

        return $user;
    }

    /**
     * Create manager user with minimal setup
     */
    protected function createManagerUser(array $attributes = []): User
    {
        $this->setUpTestPermissions();

        $user = User::factory()->create(array_merge([
            'email' => '<EMAIL>',
            'name' => 'Test Manager',
        ], $attributes));

        $user->assignRole('manager');

        return $user;
    }

    /**
     * Create reviewer user with minimal setup
     */
    protected function createReviewerUser(array $attributes = []): User
    {
        $this->setUpTestPermissions();

        $user = User::factory()->create(array_merge([
            'email' => '<EMAIL>',
            'name' => 'Test Reviewer',
        ], $attributes));

        $user->assignRole('reviewer');

        return $user;
    }

    /**
     * Create caretaker user with minimal setup
     */
    protected function createCaretakerUser(array $attributes = []): User
    {
        $this->setUpTestPermissions();

        $user = User::factory()->create(array_merge([
            'email' => '<EMAIL>',
            'name' => 'Test Caretaker',
        ], $attributes));

        $user->assignRole('caretaker');

        return $user;
    }

    /**
     * Create resident user with minimal setup
     */
    protected function createResidentUser(array $attributes = []): User
    {
        $this->setUpTestPermissions();

        $user = User::factory()->create(array_merge([
            'email' => '<EMAIL>',
            'name' => 'Test Resident',
        ], $attributes));

        $user->assignRole('resident');

        return $user;
    }

    /**
     * Create test estate with minimal setup
     */
    protected function createTestEstate(array $attributes = []): Estate
    {
        return Estate::factory()->create(array_merge([
            'name' => 'Test Estate',
            'code' => 'TST-' . rand(100, 999),
        ], $attributes));
    }

    /**
     * Assign user to estate
     */
    protected function assignUserToEstate(User $user, Estate $estate): void
    {
        $user->assignedEstates()->attach($estate->id, [
            'assigned_from' => now()->toDateString(),
            'is_active' => true,
        ]);
    }

    /**
     * Create user with specific permissions (optimized)
     */
    protected function createUserWithPermissions(array $permissions, array $attributes = []): User
    {
        $this->setUpTestPermissions();

        $user = User::factory()->create($attributes);
        $user->givePermissionTo($permissions);

        return $user;
    }

    /**
     * Create user with specific role (optimized)
     */
    protected function createUserWithRole(string $roleName, array $attributes = []): User
    {
        $this->setUpTestPermissions();

        $user = User::factory()->create($attributes);
        $user->assignRole($roleName);

        return $user;
    }
}
