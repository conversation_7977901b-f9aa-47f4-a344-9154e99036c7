<?php

namespace Tests\Traits;

use App\Models\User;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

/**
 * Modern test trait for Spatie permissions using factories
 * Replaces the manual permission setup in SetsUpSpatiePermissions
 */
trait HasSpatiePermissions
{
    /**
     * Set up basic permission system for tests
     * Optimized to avoid memory leaks by checking if permissions already exist
     */
    protected function setUpPermissions(): void
    {
        // Clear any existing permissions/roles cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Only create if they don't exist to avoid memory issues
        if (Role::count() === 0) {
            $this->createBasicRoles();
        }

        if (Permission::count() === 0) {
            $this->createBasicPermissions();
        }
    }

    /**
     * Create basic roles used across the application
     */
    protected function createBasicRoles(): void
    {
        Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
        Role::firstOrCreate(['name' => 'manager', 'guard_name' => 'web']);
        Role::firstOrCreate(['name' => 'reviewer', 'guard_name' => 'web']);
        Role::firstOrCreate(['name' => 'caretaker', 'guard_name' => 'web']);
        Role::firstOrCreate(['name' => 'resident', 'guard_name' => 'web']);
    }

    /**
     * Create basic permissions used across the application
     */
    protected function createBasicPermissions(): void
    {
        // Estate permissions
        $estatePermissions = [
            'estates.view_all', 'estates.view_assigned', 'estates.manage_all',
            'estates.manage_assigned', 'estates.create', 'estates.edit_assigned',
        ];

        // House permissions
        $housePermissions = [
            'houses.view_all', 'houses.view_assigned', 'houses.manage_all',
            'houses.manage_assigned', 'houses.create', 'houses.edit_assigned',
        ];

        // Invoice permissions
        $invoicePermissions = [
            'invoices.view_all', 'invoices.view_assigned', 'invoices.view_own',
            'invoices.create_all', 'invoices.generate_assigned', 'invoices.edit_all',
            'invoices.edit_assigned', 'invoices.approve_all', 'invoices.approve_assigned',
            'invoices.delete',
        ];

        // Reading permissions
        $readingPermissions = [
            'readings.view_all', 'readings.view_assigned', 'readings.create_all',
            'readings.create_assigned', 'readings.review_all', 'readings.approve_assigned',
        ];

        // Payment permissions
        $paymentPermissions = [
            'payments.view_all', 'payments.view_assigned', 'payments.view_own',
            'payments.create_assigned', 'payments.edit_assigned', 'payments.approve_all',
        ];

        // Admin permissions
        $adminPermissions = [
            'users.manage_all', 'system.settings.view', 'audit.view_logs',
            'export.data_all', 'analytics.view_all', 'reports.view_all',
        ];

        // Create all permissions
        $allPermissions = array_merge(
            $estatePermissions, $housePermissions, $invoicePermissions,
            $readingPermissions, $paymentPermissions, $adminPermissions
        );

        // Optimize permission creation to avoid duplicates
        $existingPermissions = Permission::whereIn('name', $allPermissions)->pluck('name')->toArray();
        $missingPermissions = array_diff($allPermissions, $existingPermissions);

        foreach ($missingPermissions as $permission) {
            Permission::create(['name' => $permission, 'guard_name' => 'web']);
        }
    }

    /**
     * Create a user with specific permissions
     */
    protected function createUserWithPermissions(array $permissions): User
    {
        $user = User::factory()->create();

        // Ensure permissions exist (optimized)
        $existingPermissions = Permission::whereIn('name', $permissions)->pluck('name')->toArray();
        $missingPermissions = array_diff($permissions, $existingPermissions);

        foreach ($missingPermissions as $permission) {
            Permission::create(['name' => $permission, 'guard_name' => 'web']);
        }

        $user->givePermissionTo($permissions);

        return $user;
    }

    /**
     * Create a user with specific role
     */
    protected function createUserWithRole(string $roleName): User
    {
        $user = User::factory()->create();
        $role = Role::firstOrCreate(['name' => $roleName, 'guard_name' => 'web']);
        $user->assignRole($role);

        return $user;
    }

    /**
     * Assert user has permission
     */
    protected function assertUserHasPermission(User $user, string $permission): void
    {
        $this->assertTrue(
            $user->hasPermissionTo($permission),
            "User does not have permission: {$permission}"
        );
    }

    /**
     * Assert user has role
     */
    protected function assertUserHasRole(User $user, string $role): void
    {
        $this->assertTrue(
            $user->hasRole($role),
            "User does not have role: {$role}"
        );
    }

    /**
     * Assert user can access resource
     */
    protected function assertUserCanAccess(User $user, string $permission): void
    {
        $this->assertTrue(
            $user->can($permission),
            "User cannot access resource with permission: {$permission}"
        );
    }
}
