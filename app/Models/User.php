<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Spatie\Permission\Traits\HasRoles;

/**
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $phone
 * @property string|null $profile_photo_path
 * @property int $is_active
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Estate> $assignedEstates
 * @property-read int|null $assigned_estates_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\House> $assignedHouses
 * @property-read int|null $assigned_houses_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Contact> $contacts
 * @property-read int|null $contacts_count
 * @property-read \App\Models\House|null $house
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 *
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User permission($permissions, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User role($roles, $guard = null, $without = false)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereProfilePhotoPath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User withoutRole($roles, $guard = null)
 *
 * @mixin \Eloquent
 */
class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, HasRoles, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the user's initials
     */
    public function initials(): string
    {
        return Str::of($this->name)
            ->explode(' ')
            ->take(2)
            ->map(fn ($word) => Str::substr($word, 0, 1))
            ->implode('');
    }

    /**
     * Check if user is admin staff
     */
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    /**
     * Check if user is management staff
     */
    public function isManager(): bool
    {
        return $this->hasRole('manager');
    }

    /**
     * Check if user is caretaker staff
     */
    public function isCaretaker(): bool
    {
        return $this->hasRole('caretaker');
    }

    /**
     * Check if user is reviewer staff
     */
    public function isReviewer(): bool
    {
        return $this->hasRole('reviewer');
    }

    /**
     * Check if user is resident
     */
    public function isResident(): bool
    {
        return $this->hasRole('resident');
    }

    /**
     * Get assigned estates for user
     */
    public function assignedEstates()
    {
        return $this->belongsToMany(Estate::class, 'user_estate_assignments')
            ->withPivot(['role_type', 'assigned_from', 'assigned_to', 'is_active', 'notes'])
            ->withTimestamps();
    }

    /**
     * Get user's effective permissions using Spatie
     * This replaces the custom permission overrides system
     */
    public function getEffectivePermissions(): array
    {
        return $this->getAllPermissions()->pluck('name')->toArray();
    }

    /**
     * Get assigned houses through assigned estates
     */
    public function assignedHouses()
    {
        return $this->hasManyThrough(
            House::class,
            Estate::class,
            'id', // Foreign key on estates table
            'estate_id', // Foreign key on houses table
            'id', // Local key on users table
            'id' // Local key on estates table
        )->whereIn('estates.id', function ($query): void {
            $query->select('estate_id')
                ->from('user_estate_assignments')
                ->where('user_id', $this->id);
        });
    }

    /**
     * Check if user has a specific permission (using Spatie)
     * This method is provided by the HasRoles trait, but we can add custom logic if needed
     */
    public function hasCustomPermission(string $permission): bool
    {
        // Use Spatie's built-in permission checking
        return $this->hasPermissionTo($permission);
    }

    /**
     * Check if user can access a specific estate
     */
    public function canAccessEstate(Estate $estate): bool
    {
        if ($this->hasRole('admin')) {
            return true;
        }

        return $this->assignedEstates()->where('estates.id', $estate->id)->exists();
    }

    /**
     * Get contacts for resident users
     */
    public function contacts()
    {
        return $this->hasMany(Contact::class);
    }

    /**
     * Get house for resident users (through primary contact)
     */
    public function house()
    {
        return $this->hasOneThrough(
            House::class,
            Contact::class,
            'user_id', // Foreign key on contacts table
            'id', // Foreign key on houses table
            'id', // Local key on users table
            'house_id' // Local key on contacts table
        )->where('contacts.is_primary', true);
    }

    /**
     * Check if user can access a specific house
     */
    public function canAccessHouse(House $house): bool
    {
        if ($this->hasRole('admin')) {
            return true;
        }

        if ($this->hasRole('resident')) {
            return $this->contacts()->where('house_id', $house->id)->exists();
        }

        return $this->assignedEstates()->where('estates.id', $house->estate_id)->exists();
    }



    /**
     * Override permission checking to allow admin users to have all permissions
     */
    public function can($abilities, $arguments = []): bool
    {
        if ($this->isAdmin()) {
            return true;
        }

        return parent::can($abilities, $arguments);
    }
}
