<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

/**
 * Minimal permission seeder for tests to avoid memory issues
 * Only creates essential permissions needed for testing
 */
class TestPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create essential roles
        $roles = [
            'admin',
            'manager', 
            'reviewer',
            'caretaker',
            'resident'
        ];

        foreach ($roles as $roleName) {
            Role::firstOrCreate(['name' => $roleName, 'guard_name' => 'web']);
        }

        // Create a comprehensive set of permissions that covers all dashboard needs
        // This is based on the actual permissions used in the application
        $permissions = [
            // Dashboard permissions
            'view-admin-dashboard', 'view-manager-dashboard', 'view-reviewer-dashboard',
            'view-caretaker-dashboard', 'view-resident-dashboard',

            // Estate permissions
            'estates.view_all', 'estates.view_assigned', 'estates.manage_assigned',
            'estates.create', 'estates.edit_assigned', 'estates.delete_assigned',

            // House permissions
            'houses.view_all', 'houses.view_assigned', 'houses.manage_assigned',
            'houses.create_assigned', 'houses.edit_assigned', 'houses.delete_assigned',

            // Invoice permissions
            'invoices.view_all', 'invoices.view_assigned', 'invoices.approve_assigned',
            'invoices.generate_assigned', 'invoices.create_assigned', 'invoices.edit_assigned',

            // User management
            'users.manage_all', 'users.view_all', 'users.create', 'users.edit', 'users.delete',

            // Analytics and Reports
            'analytics.view_all', 'analytics.view_assigned', 'reports.view_all', 'reports.view_assigned',
            'reports.revenue_all', 'reports.revenue_assigned', 'reports.consumption_all', 'reports.consumption_assigned',

            // Readings
            'readings.view_all', 'readings.view_assigned', 'readings.create_assigned',
            'readings.approve_assigned', 'readings.edit_assigned',

            // Payments
            'payments.view_all', 'payments.view_assigned', 'payments.view_history_assigned',
            'payments.view_own', 'payments.create_assigned',

            // Contacts
            'contacts.view_all', 'contacts.view_assigned', 'contacts.manage_assigned',
            'contacts.create_assigned', 'contacts.edit_assigned',

            // Accounts
            'accounts.view_own', 'accounts.view_all', 'accounts.manage_assigned',

            // Export
            'export.data_all', 'export.data_assigned',

            // Audit logs
            'audit_logs.view_all', 'audit_logs.view_assigned',
        ];

        foreach ($permissions as $permission) {
            \Spatie\Permission\Models\Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'web'
            ]);
        }
    }
}
